<?php

namespace Modules\Organization\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Domain extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'organization_id',
        'hostname',
        'name',
        'description',
        'brand_name',
        'slogan',
        'logo_url',
        'favicon_url',
        'theme_colors',
        'custom_css',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_image_url',
        'contact_info',
        'custom_config',
        'is_active',
        'domain_type',
        'status',
    ];

    protected $casts = [
        'theme_colors' => 'array',
        'custom_css' => 'array',
        'contact_info' => 'array',
        'custom_config' => 'array',
        'is_active' => 'boolean',

        'verified_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the domain.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }



    /**
     * Scope to get active domains.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }



    /**
     * Get domain by hostname.
     */
    public function scopeByHostname($query, string $hostname)
    {
        return $query->where('hostname', $hostname);
    }



    /**
     * Scope to get domains by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('domain_type', $type);
    }

    /**
     * Scope to get domains by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get domains by organization.
     */
    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get the full theme configuration with defaults.
     */
    public function getThemeConfigAttribute(): array
    {
        $defaultTheme = [
            'primary' => '#3b82f6',
            'secondary' => '#64748b',
            'accent' => '#f59e0b',
            'background' => '#ffffff',
            'surface' => '#f8fafc',
            'text' => '#1e293b',
            'text_secondary' => '#64748b',
        ];

        return array_merge($defaultTheme, $this->theme_colors ?? []);
    }

    /**
     * Get the full branding configuration.
     */
    public function getBrandingConfigAttribute(): array
    {
        return [
            'name' => $this->brand_name ?? $this->name,
            'slogan' => $this->slogan,
            'logo_url' => $this->logo_url,
            'favicon_url' => $this->favicon_url,
        ];
    }

    /**
     * Get the SEO configuration.
     */
    public function getSeoConfigAttribute(): array
    {
        return [
            'title' => $this->meta_title ?? $this->name,
            'description' => $this->meta_description,
            'keywords' => $this->meta_keywords,
            'og_image' => $this->og_image_url,
        ];
    }



    /**
     * Get the full domain configuration for frontend.
     */
    public function getConfigAttribute(): array
    {
        return [
            'id' => $this->id,
            'hostname' => $this->hostname,
            'name' => $this->name,
            'description' => $this->description,
            'branding' => $this->branding_config,
            'theme' => $this->theme_config,
            'seo' => $this->seo_config,
            'contact' => $this->contact_info ?? [],
            'custom' => $this->custom_config ?? [],
            'custom_css' => $this->custom_css ?? [],
        ];
    }




    /**
     * Check if domain is custom (not subdomain).
     */
    public function isCustomDomain(): bool
    {
        return $this->domain_type === 'custom';
    }

    /**
     * Check if domain is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->is_active;
    }



}
