<?php

namespace Modules\Core\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use Modules\Core\Models\Domain;
use Symfony\Component\HttpFoundation\Response;

class DomainMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $hostname = $request->getHost();

        // Get domain configuration
        $domainConfig = $this->getDomainConfig($hostname);

        if ($domainConfig) {
            // Set domain configuration in request
            $request->attributes->set('domain_config', $domainConfig);

            // Set default locale
            App::setLocale('vi');

            // Set timezone if configured
            if (isset($domainConfig['custom']['timezone'])) {
                Config::set('app.timezone', $domainConfig['custom']['timezone']);
            }

            // Add domain-specific headers
            $response = $next($request);

            // Add CORS headers if configured
            if (isset($domainConfig['custom']['cors'])) {
                $corsConfig = $domainConfig['custom']['cors'];

                if (isset($corsConfig['allow_origin'])) {
                    $response->headers->set('Access-Control-Allow-Origin', $corsConfig['allow_origin']);
                }

                if (isset($corsConfig['allow_methods'])) {
                    $response->headers->set('Access-Control-Allow-Methods', $corsConfig['allow_methods']);
                }

                if (isset($corsConfig['allow_headers'])) {
                    $response->headers->set('Access-Control-Allow-Headers', $corsConfig['allow_headers']);
                }
            }

            // Add security headers if configured
            if (isset($domainConfig['custom']['security_headers'])) {
                $securityHeaders = $domainConfig['custom']['security_headers'];

                foreach ($securityHeaders as $header => $value) {
                    $response->headers->set($header, $value);
                }
            }

            // Force HTTPS if configured
            if ($domainConfig['custom']['force_https'] ?? true) {
                if (!$request->isSecure() && !app()->environment('local')) {
                    return redirect()->secure($request->getRequestUri(), 301);
                }
            }

            return $response;
        }

        // If no domain configuration found, continue with default behavior
        return $next($request);
    }

    /**
     * Get domain configuration by hostname.
     */
    private function getDomainConfig(string $hostname): ?array
    {
        $cacheKey = "domain_config_{$hostname}";

        return Cache::remember($cacheKey, 3600, function () use ($hostname) {
            $domain = Domain::byHostname($hostname)->active()->first();

            return $domain?->config;
        });
    }
}
